import { useState } from 'react';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { Button } from '../../components/Button';
import Pagination from '../../components/Pagination';
import AddCategoryModal from './components/AddCategoryModal';
import { categoriesData } from '../../mockData/categoriesData';

interface Category {
  id: string;
  icon: string;
  iconBg: string;
  name: string;
  description: string;
  status: 'Active' | 'Inactive';
}

const ITEMS_PER_PAGE = 6;

const Categories = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [categories, setCategories] = useState<Category[]>(categoriesData);

  const handleAddCategory = (newCategory: { name: string; description: string; icon: string }) => {
    const category: Category = {
      id: (categories.length + 1).toString(),
      icon: newCategory.icon,
      iconBg: 'bg-teal-600',
      name: newCategory.name,
      description: newCategory.description,
      status: 'Active',
    };
    setCategories([...categories, category]);
  };

  const paginatedCategories = categories.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const totalPages = Math.ceil(categories.length / ITEMS_PER_PAGE);

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Categories</h1>
          <Breadcrumb pageName="Categories" />
        </div>
        <Button
          className="brand-gradient text-sm text-white py-3 px-5 rounded-md"
          onClick={() => setIsModalOpen(true)}
        >
          + Add Category
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full min-w-[640px] mt-6">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 text-xs sm:text-sm font-medium text-gray-700">Icon</th>
                <th className="text-left py-3 px-4 text-xs sm:text-sm font-medium text-gray-700">Category Name</th>
                <th className="text-left py-3 px-4 text-xs sm:text-sm font-medium text-gray-700">Description</th>
                <th className="text-left py-3 px-4 text-xs sm:text-sm font-medium text-gray-700">Status</th>
                <th className="text-left py-3 px-4 text-xs sm:text-sm font-medium text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody>
              {paginatedCategories.map((category) => (
                <tr key={category.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-4">
                    <img
                      src={category.icon}
                      alt={category.name}
                      className="h-7"
                    />
                  </td>
                  <td className="py-4 px-4 text-sm text-gray-900 font-medium">{category.name}</td>
                  <td className="py-4 px-4 text-sm text-gray-600 max-w-xs">{category.description}</td>
                  <td className="py-4 px-4">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${category.status === 'Active'
                        ? 'bg-teal-100 text-teal-800'
                        : 'bg-gray-100 text-gray-800'
                        }`}
                    >
                      {category.status}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <button
                      className="text-gray-600 text-sm cursor-pointer font-bold"
                      onClick={() => alert(`Edit ${category.name}`)}
                    >
                      Edit
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      </div>

      <AddCategoryModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleAddCategory}
      />
    </div>
  );
};

export default Categories;
