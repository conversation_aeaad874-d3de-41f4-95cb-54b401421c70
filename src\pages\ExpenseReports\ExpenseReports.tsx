import { useState } from 'react';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { Button } from '../../components/Button';
import { FiDownload } from 'react-icons/fi';
import { expenseData, statsData, approvalData, disputeData } from '../../mockData/mockData';
import StatsCard from './components/StatsCard';
import Tabs from './components/Tabs';
import TabContent from './components/TabContent';


const ExpenseReports = () => {
  const [activeTab, setActiveTab] = useState<'all' | 'approvals' | 'disputes'>('all');

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'Manager Approved':
      case 'Finance Approved':
      case 'Approved':
        return 'bg-teal-100 text-teal-800 px-3 py-1 rounded-full text-sm font-medium';
      case 'Manager Rejected':
        return 'bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium';
      case 'In review':
        return 'bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium';
      case 'Pending':
      default:
        return 'bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium';
    }
  };

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Expense Reports</h1>
          <Breadcrumb pageName="Expense Reports" />
        </div>
        <Button
          className="brand-gradient px-4 py-3 rounded-md text-sm text-white"
          onClick={() => alert("Exporting...")}
        >
          <FiDownload className="w-4 h-4" />
          Export
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {statsData.map((stat, index) => (
          <StatsCard key={index} {...stat} />
        ))}
      </div>

      <Tabs
        activeTab={activeTab}
        onChange={setActiveTab}
      />
      <TabContent
        tabCategory="all"
        activeTab={activeTab}
        expenseData={expenseData}
        getStatusBadgeClass={getStatusBadgeClass}
      />
      <TabContent
        tabCategory="approvals"
        activeTab={activeTab}
        approvalData={approvalData}
        getStatusBadgeClass={getStatusBadgeClass}
      />
      <TabContent
        tabCategory="disputes"
        activeTab={activeTab}
        disputeData={disputeData}
        getStatusBadgeClass={getStatusBadgeClass}
      />
    </div>
  );
};

export default ExpenseReports;
