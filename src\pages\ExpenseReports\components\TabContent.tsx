import { useState } from 'react';
import { FiFilter } from 'react-icons/fi';
import { Button } from '../../../components/Button';
import type { ExpenseData, ApprovalData, DisputeData } from '../../../mockData/mockData';
import Pagination from '../../../components/Pagination';
import AllExpenseTable from './AllExpenseTable';
import ApprovalTable from './ApprovalTable';
import DisputesTable from './DisputesTable';

interface TabContentProps {
  activeTab: string;
  tabCategory: string;
  expenseData?: ExpenseData[];
  approvalData?: ApprovalData[];
  disputeData?: DisputeData[];
  getStatusBadgeClass: (status: string) => string;
}

const ITEMS_PER_PAGE = 5;

const TabContent = ({
  activeTab,
  tabCategory,
  expenseData,
  approvalData,
  disputeData,
  getStatusBadgeClass,
}: TabContentProps) => {
  const [currentPage, setCurrentPage] = useState(1);

  const getTabTitle = () => {
    switch (tabCategory) {
      case 'all':
        return 'All Expenses';
      case 'approvals':
        return 'Management Approvals';
      case 'disputes':
        return 'Raised Disputes';
      default:
        return 'All Expenses';
    }
  };

  const getCurrentData = () => {
    switch (tabCategory) {
      case 'all':
        return expenseData || [];
      case 'approvals':
        return approvalData || [];
      case 'disputes':
        return disputeData || [];
      default:
        return expenseData || [];
    }
  };

  const currentData = getCurrentData();
  const totalPages = Math.ceil(currentData.length / ITEMS_PER_PAGE);
  const paginatedData = currentData.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  return (
    <div className={`${activeTab === tabCategory ? 'block' : 'hidden'}`}>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
            <h2 className="text-lg font-semibold text-gray-900">{getTabTitle()}</h2>
            <Button className="border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 w-full sm:w-auto">
              <FiFilter className="w-4 h-4" />
              Filters
            </Button>
          </div>

          <div className="overflow-x-auto">
            {tabCategory === 'all' && (
              <AllExpenseTable
                data={paginatedData as ExpenseData[]}
                getStatusBadgeClass={getStatusBadgeClass}
              />
            )}

            {tabCategory === 'approvals' && (
              <ApprovalTable data={paginatedData as ApprovalData[]} />
            )}

            {tabCategory === 'disputes' && (
              <DisputesTable data={paginatedData as DisputeData[]} />
            )}

          </div>

          {totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={(page) => setCurrentPage(page)}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default TabContent;
